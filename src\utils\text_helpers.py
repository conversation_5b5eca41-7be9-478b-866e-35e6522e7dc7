# -*- coding: utf-8 -*-
"""
وحدة مساعدة للنصوص والقوائم
تحتوي على دوال إنشاء النصوص ولوحات المفاتيح للواجهة
"""

import logging
from telegram import InlineKeyboardButton, InlineKeyboardMarkup
from localization.translations import get_translations

# استيراد قاموس الترجمات
translations = get_translations()

def get_text(key, lang='ar', **kwargs):
    """
    الحصول على النص المترجم

    Args:
        key: مفتاح النص
        lang: اللغة (افتراضي: العربية)
        **kwargs: متغيرات لتنسيق النص

    Returns:
        str: النص المترجم
    """
    try:
        # التحقق من وجود اللغة
        if lang not in translations:
            lang = 'ar'  # العودة للعربية كافتراضي

        # التحقق من وجود المفتاح
        if key not in translations[lang]:
            # محاولة العثور على المفتاح في اللغة الافتراضية
            if key in translations['ar']:
                text = translations['ar'][key]
            else:
                return f"[Missing: {key}]"
        else:
            text = translations[lang][key]

        # تنسيق النص بالمتغيرات المرسلة
        if kwargs:
            try:
                return text.format(**kwargs)
            except (KeyError, ValueError):
                # في حالة فشل التنسيق، إرجاع النص الأصلي
                return text

        return text

    except Exception as e:
        # في حالة حدوث خطأ، إرجاع رسالة خطأ
        return f"[Error: {key}]"

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

# نظام تخزين مؤقت للقائمة الرئيسية لتحسين الأداء
from datetime import datetime, timedelta
_menu_cache = {}
_menu_cache_expiry = {}
_CACHE_DURATION = timedelta(minutes=5)  # مدة التخزين المؤقت 5 دقائق

# استيراد التبعيات المطلوبة
def get_dependencies():
    """الحصول على التبعيات المطلوبة"""
    try:
        # استيراد SubscriptionSystem من الموقع الصحيح
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        from services.subscription_system import get_subscription_system

        # محاولة الحصول على المكونات المُهيأة من النظام الموسع أولاً
        api_manager = None
        free_day_system = None

        try:
            # استيراد دالة الحصول على المكونات المُهيأة
            from core.system_initialization_extended import get_system_components
            system_components = get_system_components()

            if system_components:
                api_manager = system_components.get('api_manager')
                logger.info("تم الحصول على api_manager من المكونات المُهيأة")
            else:
                logger.warning("المكونات المُهيأة غير متاحة، محاولة الحصول من main")
        except Exception as e:
            logger.warning(f"خطأ في الحصول على المكونات المُهيأة: {str(e)}")

        # إذا لم نحصل على api_manager من المكونات المُهيأة، نحاول من main
        if api_manager is None:
            try:
                from main import api_manager, free_day_system
            except ImportError:
                # إذا فشل الاستيراد، نحاول الحصول على المتغيرات العامة
                import main
                api_manager = getattr(main, 'api_manager', None)
                free_day_system = getattr(main, 'free_day_system', None)

        # الحصول على free_day_system من main إذا لم نحصل عليه بعد
        if free_day_system is None:
            try:
                from main import free_day_system
            except ImportError:
                import main
                free_day_system = getattr(main, 'free_day_system', None)

        # الحصول على instance من SubscriptionSystem
        SubscriptionSystem = get_subscription_system

        # التحقق من تهيئة api_manager
        if api_manager is None:
            logger.warning("api_manager غير مُهيأ، سيتم استخدام القيم الافتراضية")

        return SubscriptionSystem, api_manager, free_day_system
    except ImportError as e:
        logger.error(f"خطأ في استيراد التبعيات: {str(e)}")
        return None, None, None
    except Exception as e:
        logger.error(f"خطأ عام في get_dependencies: {str(e)}")
        return None, None, None


async def get_main_menu_text(user_id: str, lang: str) -> str:
    """إنشاء نص القائمة الرئيسية مع تخزين مؤقت لتحسين الأداء"""
    try:
        # التحقق من التخزين المؤقت أولاً
        current_time = datetime.now()
        cache_key = f"{user_id}_{lang}"

        if (cache_key in _menu_cache and
            cache_key in _menu_cache_expiry and
            current_time < _menu_cache_expiry[cache_key]):
            logger.info(f"استخدام النص المخزن مؤقتاً للمستخدم {user_id}")
            return _menu_cache[cache_key]

        logger.info(f"جاري إنشاء نص القائمة الرئيسية للمستخدم {user_id} باللغة {lang}")

        # الحصول على التبعيات
        SubscriptionSystem, api_manager, free_day_system = get_dependencies()
        if not SubscriptionSystem:
            return "👋 مرحباً بك في بوت التحليل الفني!\n\nيرجى المحاولة مرة أخرى لاحقاً." if lang == 'ar' else "👋 Welcome to Technical Analysis Bot!\n\nPlease try again later."

        # تسجيل حالة api_manager دون إعادة التهيئة
        if api_manager is None:
            logger.info(f"api_manager غير متاح للمستخدم {user_id}، سيتم استخدام القيم الافتراضية")
        else:
            logger.info(f"api_manager متاح للمستخدم {user_id}")

        # التحقق من حالة الاشتراك
        logger.info(f"جاري التحقق من حالة اشتراك المستخدم {user_id}...")
        subscription_system = SubscriptionSystem()
        try:
            # استخدام الطريقة المتزامنة لتجنب مشاكل حلقة الأحداث
            is_premium = subscription_system.is_subscribed_sync(user_id)

            # التحقق من اليوم المجاني بشكل منفصل
            is_free_day = False
            try:
                if free_day_system:
                    is_free_day_today = free_day_system.is_today_free_day(user_id)
                    is_free_day_active = free_day_system.is_free_day_active(user_id)
                    is_free_day = is_free_day_today or is_free_day_active
            except Exception as e:
                logger.error(f"خطأ في التحقق من اليوم المجاني: {str(e)}")
                is_free_day = False

            logger.info(f"حالة اشتراك المستخدم {user_id}: {is_premium}, يوم مجاني: {is_free_day}")
        except Exception as e:
            logger.error(f"خطأ في التحقق من حالة اشتراك المستخدم {user_id}: {str(e)}")
            is_premium = False
            is_free_day = False

        # التحقق من وجود مفاتيح API
        has_binance_api = False
        has_gemini_api = False

        # التأكد من تهيئة مدير API
        logger.info(f"التحقق من تهيئة مدير API للمستخدم {user_id}...")
        if api_manager is not None:
            logger.info(f"مدير API متاح للمستخدم {user_id}")
            try:
                logger.info(f"جاري التحقق من مفاتيح Binance API للمستخدم {user_id}...")
                has_binance_api = await api_manager.has_api_keys(user_id, 'binance')
                logger.info(f"جاري التحقق من مفاتيح Gemini API للمستخدم {user_id}...")
                has_gemini_api = await api_manager.has_api_keys(user_id, 'gemini')
                logger.info(f"حالة مفاتيح API للمستخدم {user_id}: Binance={has_binance_api}, Gemini={has_gemini_api}")
            except Exception as e:
                logger.error(f"خطأ في التحقق من مفاتيح API: {str(e)}")
                logger.error(f"تفاصيل الخطأ: {type(e).__name__}")
        else:
            logger.warning(f"مدير API غير متاح للمستخدم {user_id} - سيتم استخدام القيم الافتراضية")
            # استخدام القيم الافتراضية عندما يكون api_manager غير متاح
            has_binance_api = False
            has_gemini_api = False

        # نص الترحيب المفصل من ملف الترجمات
        menu_text = get_text('welcome', lang)

        # إضافة معلومات حالة الاشتراك أو اليوم المجاني
        if is_premium:
            logger.info(f"المستخدم {user_id} مشترك أو لديه يوم مجاني، جاري جلب تفاصيل الاشتراك...")
            try:
                subscription_info = subscription_system.get_subscription_details(user_id)
                logger.info(f"تم جلب تفاصيل الاشتراك للمستخدم {user_id}: {subscription_info}")

                # التحقق مما إذا كان اليوم المجاني مفعل
                if is_free_day:
                    # المستخدم لديه يوم مجاني نشط
                    if lang == 'ar':
                        menu_text = menu_text.replace(
                            "اليوم المجاني القادم: الاثنين (09-06-2025) 🎁",
                            "🎁 اليوم المجاني مفعل اليوم! استمتع بالميزات المدفوعة مجاناً اليوم."
                        )
                        # تحديث معلومات الاستخدام لليوم المجاني
                        menu_text = menu_text.replace(
                            "تحليلات: 3/3 🔍\nتنبيهات: 1/1 🔔",
                            "تحليلات: ∞ (يوم مجاني) 🔍\nتنبيهات: ∞ (يوم مجاني) 🔔"
                        )
                    else:
                        menu_text = menu_text.replace(
                            "Next Free Day: Monday (09-06-2025) 🎁",
                            "🎁 Free Day Active Today! Enjoy premium features for free today."
                        )
                        # تحديث معلومات الاستخدام لليوم المجاني
                        menu_text = menu_text.replace(
                            "Analyses: 3/3 🔍\nAlerts: 1/1 🔔",
                            "Analyses: ∞ (Free Day) 🔍\nAlerts: ∞ (Free Day) 🔔"
                        )
                else:
                    # المستخدم مشترك فعلياً (وليس يوم مجاني)
                    expiry_info = subscription_info.get('expiry', 'غير محدد')
                    if lang == 'ar':
                        menu_text = menu_text.replace(
                            "اليوم المجاني القادم: الاثنين (09-06-2025) 🎁",
                            f"⭐️ حالة الاشتراك: مشترك\n📅 تاريخ الانتهاء: {expiry_info}"
                        )
                        # تحديث معلومات الاستخدام للمشتركين
                        menu_text = menu_text.replace(
                            "تحليلات: 3/3 🔍\nتنبيهات: 1/1 🔔",
                            "تحليلات: ∞ (مشترك) 🔍\nتنبيهات: ∞ (مشترك) 🔔"
                        )
                    else:
                        menu_text = menu_text.replace(
                            "Next Free Day: Monday (09-06-2025) 🎁",
                            f"⭐️ Subscription Status: Premium\n📅 Expiry Date: {expiry_info}"
                        )
                        # تحديث معلومات الاستخدام للمشتركين
                        menu_text = menu_text.replace(
                            "Analyses: 3/3 🔍\nAlerts: 1/1 🔔",
                            "Analyses: ∞ (Premium) 🔍\nAlerts: ∞ (Premium) 🔔"
                        )
            except Exception as e:
                logger.error(f"خطأ في جلب تفاصيل الاشتراك للمستخدم {user_id}: {str(e)}")
                # استبدال معلومات اليوم المجاني بمعلومات الاشتراك الأساسية
                if is_free_day:
                    # في حالة الخطأ ولكن اليوم المجاني نشط
                    if lang == 'ar':
                        menu_text = menu_text.replace(
                            "اليوم المجاني القادم: الاثنين (09-06-2025) 🎁",
                            "🎁 اليوم المجاني مفعل اليوم!"
                        )
                    else:
                        menu_text = menu_text.replace(
                            "Next Free Day: Monday (09-06-2025) 🎁",
                            "🎁 Free Day Active Today!"
                        )
                else:
                    # في حالة الخطأ والمستخدم مشترك
                    if lang == 'ar':
                        menu_text = menu_text.replace(
                            "اليوم المجاني القادم: الاثنين (09-06-2025) 🎁",
                            "⭐️ حالة الاشتراك: مشترك"
                        )
                    else:
                        menu_text = menu_text.replace(
                            "Next Free Day: Monday (09-06-2025) 🎁",
                            "⭐️ Subscription Status: Premium"
                        )

        # تحديث معلومات الاستخدام المجاني للمستخدمين غير المشتركين وبدون يوم مجاني
        if not is_premium:
            try:
                # للمستخدمين العاديين (بدون اشتراك وبدون يوم مجاني) - عرض الحدود الطبيعية
                free_usage = subscription_system.get_free_usage(user_id)
                logger.info(f"تم جلب بيانات الاستخدام المجاني للمستخدم {user_id}: {free_usage}")

                # تحديث معلومات الاستخدام في النص
                if lang == 'ar':
                    menu_text = menu_text.replace(
                        "تحليلات: 3/3 🔍\nتنبيهات: 1/1 🔔",
                        f"تحليلات: {free_usage['analyses']}/3 🔍\nتنبيهات: {free_usage['alerts']}/1 🔔"
                    )
                else:
                    menu_text = menu_text.replace(
                        "Analyses: 3/3 🔍\nAlerts: 1/1 🔔",
                        f"Analyses: {free_usage['analyses']}/3 🔍\nAlerts: {free_usage['alerts']}/1 🔔"
                    )

                # تحديث معلومات اليوم المجاني القادم
                try:
                    free_day_status = free_day_system.get_user_free_day_status(user_id)
                    next_free_day = free_day_status['next_free_day']
                    day_name = free_day_status['day_name']

                    if lang == 'ar':
                        menu_text = menu_text.replace(
                            "اليوم المجاني القادم: الاثنين (09-06-2025) 🎁",
                            f"اليوم المجاني القادم: {day_name} ({next_free_day.strftime('%Y-%m-%d')}) 🎁"
                        )
                    else:
                        menu_text = menu_text.replace(
                            "Next Free Day: Monday (09-06-2025) 🎁",
                            f"Next Free Day: {day_name} ({next_free_day.strftime('%Y-%m-%d')}) 🎁"
                        )
                except Exception as e:
                    logger.error(f"خطأ في جلب معلومات اليوم المجاني القادم للمستخدم {user_id}: {str(e)}")

            except Exception as e:
                logger.error(f"خطأ في جلب بيانات الاستخدام المجاني للمستخدم {user_id}: {str(e)}")

        # تحديث معلومات API
        try:
            api_info = {}
            if api_manager:
                try:
                    api_info = await api_manager.get_api_info(user_id)
                except Exception as e:
                    logger.error(f"خطأ في الحصول على معلومات API: {str(e)}")
            else:
                logger.warning(f"مدير API غير متاح لجلب معلومات API للمستخدم {user_id}")
                # استخدام قيم افتراضية عندما يكون api_manager غير متاح
                api_info = {
                    'has_binance': has_binance_api,
                    'has_gemini': has_gemini_api,
                    'has_kucoin': False,
                    'has_coinbase': False,
                    'has_bybit': False,
                    'has_okx': False,
                    'has_kraken': False
                }

            # تحديث حالة API في النص
            api_status_parts = []
            platforms = ['binance', 'kucoin', 'coinbase', 'bybit', 'okx', 'kraken']
            platform_names = ['Binance', 'KuCoin', 'Coinbase', 'Bybit', 'OKX', 'Kraken']

            for platform_id, platform_name in zip(platforms, platform_names):
                # تخطي Gemini للمستخدمين غير المشتركين
                if platform_id == 'gemini' and not is_premium:
                    continue

                has_platform = api_info.get(f'has_{platform_id}', False)
                api_status_parts.append(f"{platform_name} {'✅' if has_platform else '❌'}")

            new_api_status = "🔑 API: " + " ".join(api_status_parts)
            menu_text = menu_text.replace(
                "🔑 API: Binance ✅ KuCoin ❌ Coinbase ❌ Bybit ❌ OKX ❌ Kraken ❌",
                new_api_status
            )

        except Exception as e:
            logger.error(f"خطأ في تحديث معلومات API: {str(e)}")

        # إضافة ميزات إضافية للمشتركين أو من لديهم يوم مجاني
        if (is_premium or is_free_day) and has_gemini_api:
            # إضافة ميزة الدردشة مع AI للمشتركين الذين لديهم مفتاح Gemini
            if lang == 'ar':
                menu_text = menu_text.replace(
                    "• إعداد API 🔑",
                    "• الدردشة مع AI 🤖\n• إعداد API 🔑"
                )
            else:
                menu_text = menu_text.replace(
                    "• API Setup 🔑",
                    "• Chat with AI 🤖\n• API Setup 🔑"
                )

        # إضافة ميزة إدارة العملات للمشتركين أو من لديهم يوم مجاني
        if is_premium or is_free_day:
            if lang == 'ar':
                menu_text = menu_text.replace(
                    "• الشروط والأحكام 📜",
                    "• إدارة العملات 💰\n• الشروط والأحكام 📜"
                )
            else:
                menu_text = menu_text.replace(
                    "• Terms & Conditions 📜",
                    "• Manage Currencies 💰\n• Terms & Conditions 📜"
                )

        # إزالة قسم الترقية للمشتركين أو من لديهم يوم مجاني وإضافة معلومات الخدمات المميزة
        if is_premium or is_free_day:
            # إزالة قسم الترقية
            if lang == 'ar':
                upgrade_section = """✨ للحصول على مميزات إضافية (5 USD أسبوعياً):
• تنبيهات سعرية غير محدودة 🔔
• إعدادات وتخصيص متقدم ⚙️
• تحليلات غير محدودة 📈
• جميع المؤشرات الفنية 🎯
• تحليل بالذكاء الاصطناعي Gemini 🤖

اضغط على ✨ ترقية الحساب للاشتراك"""
                premium_section = """⭐️ الخدمات المميزة:
• تنبيهات سعرية غير محدودة 🔔
• إعدادات وتخصيص متقدم ⚙️
• تحليلات غير محدودة 📈
• جميع المؤشرات الفنية 🎯"""
            else:
                upgrade_section = """✨ Get additional features (5 USD weekly):
• Unlimited price alerts 🔔
• Advanced customization ⚙️
• Unlimited analyses 📈
• All technical indicators 🎯
• Gemini AI analysis 🤖

Click ✨ Upgrade Account to subscribe"""
                premium_section = """⭐️ Premium Services:
• Unlimited price alerts 🔔
• Advanced customization ⚙️
• Unlimited analyses 📈
• All technical indicators 🎯"""

            menu_text = menu_text.replace(upgrade_section, premium_section)

        logger.info(f"تم إنشاء نص القائمة الرئيسية للمستخدم {user_id} بنجاح")

        # حفظ النص في التخزين المؤقت
        _menu_cache[cache_key] = menu_text
        _menu_cache_expiry[cache_key] = current_time + _CACHE_DURATION

        return menu_text

    except Exception as e:
        logger.error(f"خطأ في إنشاء نص القائمة الرئيسية: {str(e)}")
        # إرجاع نص بسيط في حالة الخطأ
        return "👋 مرحباً بك في بوت التحليل الفني!\n\nيرجى المحاولة مرة أخرى لاحقاً." if lang == 'ar' else "👋 Welcome to Technical Analysis Bot!\n\nPlease try again later."


def clear_expired_cache():
    """تنظيف التخزين المؤقت المنتهي الصلاحية"""
    try:
        current_time = datetime.now()
        expired_keys = []

        for key, expiry_time in _menu_cache_expiry.items():
            if current_time >= expiry_time:
                expired_keys.append(key)

        for key in expired_keys:
            _menu_cache.pop(key, None)
            _menu_cache_expiry.pop(key, None)

        if expired_keys:
            logger.info(f"تم تنظيف {len(expired_keys)} عنصر منتهي الصلاحية من التخزين المؤقت")

    except Exception as e:
        logger.error(f"خطأ في تنظيف التخزين المؤقت: {str(e)}")


def get_main_menu_keyboard(user_id: str, lang: str) -> InlineKeyboardMarkup:
    """إنشاء لوحة مفاتيح القائمة الرئيسية"""
    # الحصول على التبعيات
    SubscriptionSystem, api_manager, free_day_system = get_dependencies()
    if not SubscriptionSystem:
        # إرجاع لوحة مفاتيح أساسية في حالة عدم توفر التبعيات
        return InlineKeyboardMarkup([
            [InlineKeyboardButton("📊 التحليل المحسن" if lang == 'ar' else "📊 Enhanced Analysis", callback_data='enhanced_analysis_menu')],
            [InlineKeyboardButton("🔑 إعداد API" if lang == 'ar' else "🔑 API Setup", callback_data='setup_api_keys')],
        ])

    subscription_system = SubscriptionSystem()
    try:
        # استخدام الطريقة المتزامنة لتجنب مشاكل حلقة الأحداث
        logger.debug("استخدام طريقة متزامنة للتحقق من الاشتراك في get_main_menu_keyboard")
        is_premium = subscription_system.is_subscribed_sync(user_id)

        # التحقق من اليوم المجاني بشكل منفصل
        is_free_day = False
        try:
            if free_day_system:
                is_free_day_today = free_day_system.is_today_free_day(user_id)
                is_free_day_active = free_day_system.is_free_day_active(user_id)
                is_free_day = is_free_day_today or is_free_day_active
        except Exception as e:
            logger.error(f"خطأ في التحقق من اليوم المجاني في get_main_menu_keyboard: {str(e)}")
            is_free_day = False

    except Exception as e:
        logger.error(f"خطأ في التحقق من حالة الاشتراك في get_main_menu_keyboard: {str(e)}")
        is_premium = False
        is_free_day = False

    # التحقق من وجود مفاتيح API للمستخدم
    # نستخدم طريقة متزامنة للتحقق من وجود مفاتيح API
    has_gemini_api = False
    if api_manager:
        try:
            # التحقق من مفتاح Gemini API
            gemini_key, _ = api_manager.get_api_keys_sync(user_id, 'gemini')
            has_gemini_api = bool(gemini_key)
        except Exception as e:
            logger.error(f"خطأ في التحقق من مفاتيح API: {str(e)}")
            has_gemini_api = False
    else:
        logger.warning(f"مدير API غير متاح في get_main_menu_keyboard للمستخدم {user_id}")
        has_gemini_api = False

    keyboard = [
        # التحليل المحسن كأمر مباشر (يطلب رمز العملة فوراً)
        [InlineKeyboardButton("📊 التحليل المحسن" if lang == 'ar' else "📊 Enhanced Analysis", callback_data='start_enhanced_analysis')],
    ]

    # إضافة إعدادات التحليل للمشتركين أو من لديهم يوم مجاني فقط
    if is_premium or is_free_day:
        keyboard.extend([
            [InlineKeyboardButton("⚙️ إعدادات التحليل" if lang == 'ar' else "⚙️ Analysis Settings", callback_data='show_analysis_type_settings')],
        ])

    # إضافة باقي الأزرار
    keyboard.extend([
        [InlineKeyboardButton(get_text('active_alerts', lang), callback_data='active_alerts')],
        [InlineKeyboardButton("🧠 تعلم مع الذكاء الاصطناعي" if lang == 'ar' else "🧠 Learn with AI", callback_data='learn_trading_ai')], # زر تعلم التداول بالذكاء الاصطناعي
        [InlineKeyboardButton("🔑 إعداد API" if lang == 'ar' else "🔑 API Setup", callback_data='setup_api_keys')],
    ])

    # تم إزالة زر شرح التحليل المحسن من القائمة الرئيسية لتجنب التكرار (متاح داخل قائمة التحليل المحسن)

    # إضافة زر الدردشة مع الذكاء الاصطناعي للمشتركين أو من لديهم يوم مجاني والذين لديهم مفتاح Gemini API
    if (is_premium or is_free_day) and has_gemini_api:
        keyboard.insert(2, [InlineKeyboardButton("🤖 الدردشة مع AI" if lang == 'ar' else "🤖 Chat with AI", callback_data='ai_chat')])

    # إضافة زر إدارة اليوم المجاني (للجميع)
    keyboard.append([
        InlineKeyboardButton(
            "🎁 Free Day Settings" if lang == 'en' else "🎁 إعدادات اليوم المجاني",
            callback_data='free_day_settings'
        )
    ])

    keyboard.extend([
        [InlineKeyboardButton(get_text('help', lang), callback_data='help')],
        [InlineKeyboardButton(get_text('change_language', lang), callback_data='language')],
        [InlineKeyboardButton("📜 الشروط والأحكام" if lang == 'ar' else "📜 Terms & Conditions", callback_data='terms')]
    ])

    if is_premium or is_free_day:
        keyboard.extend([
            [InlineKeyboardButton(get_text('manage_currencies', lang), callback_data='manage_currencies')],
        ])
    else:
        keyboard.append([
            InlineKeyboardButton(
                "✨ Upgrade Account" if lang == 'en' else "✨ ترقية الحساب",
                callback_data='upgrade'
            )
        ])

    return InlineKeyboardMarkup(keyboard)
